import request from '@/utils/request'

// 人群筛查列表
export const getMassScreeningList = (data: any) => {
  return request.post({
    url: '/backend/app/crowd/screening/page',
    data
  })
}

// 人群筛查详情
export const getMassScreeningDetail = (data: any) => {
  return request.post({
    url: '/backend/app/crowd/screening/detail',
    data
  })
}

// 人群筛查疾病数量
export const getMassScreeningDisease = (data: any) => {
  return request.post({
    url: '/backend/app/crowd/screening/disease',
    data
  })
}

// 删除人群筛查
export const deleteMassScreening = (data: any) => {
  return request.post({
    url: '/backend/app/crowd/screening/remove',
    data
  })
}
