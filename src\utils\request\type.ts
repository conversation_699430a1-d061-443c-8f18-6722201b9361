// types.ts
import type { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'

// 自定义拦截器类型
export interface RequestInterceptors {
  requestSuccessFn?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig
  requestFailureFn?: (err: any) => any
  responseSuccessFn?: (res: AxiosResponse) => AxiosResponse
  responseFailureFn?: (err: any) => any
}

// 扩展 Axios 配置，添加自定义拦截器字段
export interface CreateRequestConfig extends AxiosRequestConfig {
  interceptors?: RequestInterceptors
}
