import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { createStyleImportPlugin, ElementPlusResolve } from 'vite-plugin-style-import'
import { getBaseUrl } from './src/utils/env'

export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isProduction = mode === 'production'
  const isDevelopment = mode === 'development'
  const isBuild = command === 'build'

  return {
    // 基础配置
    base: env.VITE_PUBLIC_PATH || '/',

    // 插件配置
    plugins: [
      vue({
        // Vue 编译优化
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),

      // 开发工具仅在开发环境启用
      isDevelopment && vueDevTools(),

      // 自动导入配置
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: [
          'vue',
          'vue-router',
          'pinia',
          {
            'element-plus': ['ElMessage', 'ElMessageBox', 'ElNotification', 'ElLoading']
          }
        ],
        dts: true,
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true
        }
      }),

      // 组件自动导入
      Components({
        resolvers: [ElementPlusResolver()],
        dts: true,
        directoryAsNamespace: true
      }),

      // Element Plus 样式按需导入
      createStyleImportPlugin({
        resolves: [ElementPlusResolve()],
        libs: [
          {
            libraryName: 'element-plus',
            esModule: true,
            resolveStyle: (name) => {
              return `element-plus/theme-chalk/${name}.css`
            }
          }
        ]
      })
    ].filter(Boolean),

    // 路径解析
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '~': fileURLToPath(new URL('./src', import.meta.url)),
        'components': fileURLToPath(new URL('./src/components', import.meta.url)),
        'utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
        'assets': fileURLToPath(new URL('./src/assets', import.meta.url)),
        'views': fileURLToPath(new URL('./src/views', import.meta.url)),
        'store': fileURLToPath(new URL('./src/store', import.meta.url))
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },

    // CSS 配置
    css: {
      preprocessorOptions: {
        less: {
          additionalData: `@import "@/assets/css/variables.less";`,
          javascriptEnabled: true
        }
      },
      // 生产环境启用 CSS 代码分割
      codeSplit: isProduction
    },

    // 构建配置
    build: {
      outDir: isProduction ? 'dist_prod' : 'dist_dev',
      assetsDir: 'assets',
      sourcemap: isDevelopment ? true : false,

      // 压缩配置
      minify: isProduction ? 'terser' : 'esbuild',
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction,
          pure_funcs: isProduction ? ['console.log', 'console.info'] : []
        },
        mangle: {
          safari10: true
        }
      },

      // 构建目标
      target: ['es2015', 'chrome63'],

      // 警告阈值
      chunkSizeWarningLimit: 1000,

      // Rollup 配置
      rollupOptions: {
        output: {
          // 手动分包
          manualChunks: {
            // Vue 生态
            'vue-vendor': ['vue', 'vue-router', 'pinia'],

            // UI 库
            'element-plus': ['element-plus', '@element-plus/icons-vue'],

            // 工具库
            'utils': ['lodash', 'axios', 'crypto-js'],

            // 业务模块可以根据实际情况调整
            'business': ['./src/service', './src/utils']
          },

          // 资源文件命名
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
              ? chunkInfo.facadeModuleId.split('/').pop()?.replace(/\.\w+$/, '') || 'chunk'
              : 'chunk'
            return `js/[name]-[hash].js`
          },
          assetFileNames: (assetInfo) => {
            const name = assetInfo.name || 'asset'
            const info = name.split('.')
            let extType = info[info.length - 1]
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(name)) {
              extType = 'media'
            } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(name)) {
              extType = 'images'
            } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(name)) {
              extType = 'fonts'
            }
            return `${extType}/[name]-[hash].[ext]`
          },
          entryFileNames: 'js/[name]-[hash].js'
        },

        // 外部依赖（如果需要 CDN）
        external: isBuild ? [] : []
      },

      // 构建报告
      reportCompressedSize: true,

      // 清空输出目录
      emptyOutDir: true
    },

    // 开发服务器配置
    server: {
      host: '0.0.0.0',
      port: 8000,
      open: true,
      cors: true,

      // 预热常用文件
      warmup: {
        clientFiles: ['./src/components/**/*.vue', './src/utils/**/*.ts']
      },

      // 代理配置
      proxy: {
        '/api': {
          target: getBaseUrl(mode),
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          // 代理超时
          timeout: 10000,
          // 代理错误处理
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('proxy error', err)
            })
            proxy.on('proxyReq', (_proxyReq, req, _res) => {
              console.log('Sending Request to the Target:', req.method, req.url)
            })
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url)
            })
          }
        }
      }
    },

    // 预览服务器配置
    preview: {
      port: 8080,
      host: '0.0.0.0',
      open: true
    },

    // 依赖优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus',
        '@element-plus/icons-vue',
        'axios',
        'lodash',
        'crypto-js'
      ],
      exclude: ['@vueuse/core']
    },

    // 环境变量
    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
    }
  }
})
