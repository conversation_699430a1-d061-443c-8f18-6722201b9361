import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { createStyleImportPlugin, ElementPlusResolve } from 'vite-plugin-style-import'
import { getBaseUrl } from './src/utils/env'
export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production'

  return {
    plugins: [
      vue(),
      vueDevTools(),
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      Components({
        resolvers: [ElementPlusResolver()]
      }),
      createStyleImportPlugin({
        resolves: [ElementPlusResolve()],
        libs: [
          {
            libraryName: 'element-plus',
            esModule: true,
            resolveStyle: (name) => {
              return `element-plus/theme-chalk/${name}.css`
            }
          }
        ]
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    build: {
      outDir: isProduction ? 'dist_prod' : 'dist_dev',
      sourcemap: isProduction ? false : true,
      minify: isProduction ? 'terser' : 'esbuild', // 生产环境用 Terser
      terserOptions: {
        compress: {
          // 生产环境移除所有 console 语句
          drop_console: isProduction,
          drop_debugger: isProduction
        }
      }
    },
    server: {
      port: 8000,
      open: true,
      proxy: {
        '/api': {
          target: getBaseUrl(mode), // 你的后端服务地址
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '') // 可选：去掉 /api 前缀
        }
      }
    }
  }
})
