<!-- 糖尿病和高血压问卷 -->
<template>
  <div class="qusetion">
    <div class="question-item" v-for="item in questionList" :key="item.id">
      <p>{{ item.question }}</p>
      <div class="question-item-options">
        <el-radio-group v-model="item.answer">
          <el-radio v-for="option in item.options" :key="option.value" :value="option.value">
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { tnbAndgxyQuestion, copdQuestion } from './questionnaireAnswer'

const props = defineProps({
  questionnaireIndex: {
    type: Number,
    default: 0
  },
  detail: {
    type: Object,
    default: () => ({})
  }
})

const questionList = computed(() => {
  if (props.questionnaireIndex === 0) {
    return tnbAndgxyQuestion.map((item) => {
      return {
        ...item,
        answer: props.detail[`gt${item.id}`]
      }
    })
  } else if (props.questionnaireIndex === 1) {
    return copdQuestion.map((item) => {
      return {
        ...item,
        answer: props.detail[`copd${item.id}`]
      }
    })
  } else {
    return []
  }
})
</script>

<style scoped lang="less">
p {
  margin: 6px 0;
  font-size: 14px;
  font-weight: 600;
}
</style>
