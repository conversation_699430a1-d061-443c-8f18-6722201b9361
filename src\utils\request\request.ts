import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
  type InternalAxiosRequestConfig
} from 'axios'
import type { RequestInterceptors, CreateRequestConfig } from './type'
import { ElMessage } from 'element-plus'
import { localCache } from '../cache'
import { LOGIN_Token } from '../constants'
import router from '@/router'

// 创建一个 Request 类来封装 axios
class Request {
  private instance: AxiosInstance // axios 实例
  private interceptors?: RequestInterceptors // 自定义拦截器

  constructor(config: CreateRequestConfig) {
    this.instance = axios.create(config) // 创建 axios 实例
    this.interceptors = config.interceptors // 保存用户传入的拦截器

    // 注册全局请求拦截器，自动添加 token
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        console.log('全局请求拦截器')
        const token = localCache.getCache(LOGIN_Token) // 从缓存中获取 token
        if (token) {
          config.headers.Authorization = token // 设置 token 到请求头
        }
        return config
      },
      (err) => err // 请求失败的处理
    )

    // 注册全局响应拦截器
    this.instance.interceptors.response.use(
      (res: AxiosResponse) => {
        console.log('全局响应拦截器')
        const data = res.data
        if (data.code === 401) {
          ElMessage.error('登录已过期，请重新登录') // 接口返回错误时弹出提示
          localStorage.removeItem(LOGIN_Token) // 删除缓存中的 token
          router.push('/login')
          return Promise.reject(data) // 拦截错误数据
        }
        if (data.code !== 200) {
          ElMessage.error(data.message || '请求错误') // 接口返回错误时弹出提示
          return Promise.reject(data) // 拦截错误数据
        }
        return res // 返回真正需要的数据
      },
      (err) => {
        ElMessage.error(err?.response?.data?.message || '网络异常') // 网络异常兜底处理
        return Promise.reject(err)
      }
    )

    // 注册传入的请求/响应拦截器
    this.instance.interceptors.request.use(
      this.interceptors?.requestSuccessFn,
      this.interceptors?.requestFailureFn
    )
    this.instance.interceptors.response.use(
      this.interceptors?.responseSuccessFn,
      this.interceptors?.responseFailureFn
    )
  }

  // 封装的 request 方法，支持泛型 T 用于类型推导
  async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.request<T>(config)
    return response.data
  }

  // 封装 GET 请求
  get<T = any>(config: AxiosRequestConfig) {
    return this.request<T>({ ...config, method: 'GET' })
  }

  // 封装 POST 请求
  post<T = any>(config: AxiosRequestConfig) {
    return this.request<T>({ ...config, method: 'POST' })
  }

  // 封装 PUT 请求
  put<T = any>(config: AxiosRequestConfig) {
    return this.request<T>({ ...config, method: 'PUT' })
  }

  // 封装 DELETE 请求
  delete<T = any>(config: AxiosRequestConfig) {
    return this.request<T>({ ...config, method: 'DELETE' })
  }
}

export default Request
