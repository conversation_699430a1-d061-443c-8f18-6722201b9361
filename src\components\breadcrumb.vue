<template>
  <div class="header">
    <div v-if="isExpand" class="expand-icon" @click="handleExpand">
      <el-icon><Expand /></el-icon>
    </div>
    <div v-else class="fold-icon" @click="handleFold">
      <el-icon><Fold /></el-icon>
    </div>
    <div class="breadcrumb">
      <el-breadcrumb>
        <el-breadcrumb-item>homepage</el-breadcrumb-item>
        <el-breadcrumb-item>
          <a href="/">promotion management</a>
        </el-breadcrumb-item>
        <el-breadcrumb-item>promotion list</el-breadcrumb-item>
        <el-breadcrumb-item>promotion detail</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isExpand = ref(true)
const handleExpand = () => {
  isExpand.value = !isExpand.value
}
const handleFold = () => {
  isExpand.value = !isExpand.value
}
</script>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;
  .expand-icon,
  .fold-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
