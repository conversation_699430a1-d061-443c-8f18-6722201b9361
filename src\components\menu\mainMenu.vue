<template>
  <div class="main-menu">
    <el-menu :default-active="menuStore.activeMenu" @select="handleSelectMenu">
      <MenuItem v-for="item in menuData" :key="item.key" :item="item" />
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { menuData } from './mockData'
import MenuItem from './menuItem.vue'
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import useMenuStore from '@/store/menu'

const menuStore = useMenuStore()
const router = useRouter()
onMounted(() => {
  menuStore.setActiveMenu(router.currentRoute.value.path)
})

const handleSelectMenu = (index: string) => {
  menuStore.setActiveMenu(index)
  router.push(index)
}
</script>
