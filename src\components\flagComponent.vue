<template>
  <div class="result-title">
    <div class="line"></div>
    <span class="text">
      <slot>{{ props.title }}</slot>
    </span>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  title: string
}>()
</script>

<style lang="less" scoped>
.result-title {
  display: flex;
  align-items: center;
}
.line {
  width: 4px;
  height: 20px;
  background: #1890ff;
  border-radius: 2px;
  margin-right: 8px;
}
.text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
</style>
