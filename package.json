{"name": "vue-ts-cms", "version": "0.0.0", "type": "module", "private": true, "scripts": {"dev": "vite", "preview:dev": "vite preview --outDir dist_dev", "preview:prod": "vite preview --outDir dist_prod", "build:dev": "npm run type-check && vite build --mode development", "build:prod": "npm run type-check && vite build --mode production", "build:analyze": "npm run build:prod && node scripts/build-analyzer.js", "type-check": "vue-tsc --noEmit", "lint": "eslint --fix --ext .js,.ts,.vue", "lint:fix": "eslint --fix --ext .js,.ts,.vue src/", "format": "prettier --write src/", "format:check": "prettier --check src/", "clean": "rimraf dist_dev dist_prod", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "crypto-js": "^4.2.0", "element-plus": "^2.9.10", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "commitizen": "^4.3.1", "consola": "^3.4.2", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "husky": "^8.0.0", "jiti": "^2.4.2", "less": "^4.3.0", "lint-staged": "^16.0.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "terser": "^5.39.0", "typescript": "~5.8.0", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-style-import": "^2.0.0", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}, "lint-staged": {"*.{cjs,js,ts,vue}": ["eslint --fix --ext .js,.ts,.vue", "prettier --write"], "*.{css,less,html,json,md}": ["prettier --write"]}}