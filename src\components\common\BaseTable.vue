<template>
  <div class="base-table">
    <el-table
      v-loading="loading"
      :data="tableData"
      :border="border"
      :stripe="stripe"
      :height="height"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <el-table-column v-if="showSelection" type="selection" width="55" align="center" />
      <!-- 序号列 -->
      <el-table-column v-if="showIndex" type="index" label="序号" width="60" align="center" />
      <!-- 动态列 -->
      <template v-for="(item, index) in columns" :key="index">
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :align="item.align || 'center'"
          :show-overflow-tooltip="item.showOverflowTooltip"
        >
          <template #default="scope" v-if="item.slot">
            <slot :name="item.slot" :row="scope.row" :index="scope.$index" />
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, withDefaults } from 'vue'

interface Column {
  prop: string
  label: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  slot?: string
  showOverflowTooltip?: boolean
}

interface Pagination {
  pageNo: number
  pageSize: number
  total: number
}

interface Props {
  // 表格数据
  tableData: any[]
  // 列配置
  columns: Column[]
  // 是否显示边框
  border?: boolean
  // 是否显示斑马纹
  stripe?: boolean
  // 表格高度
  height?: string | number
  // 是否显示选择列
  showSelection?: boolean
  // 是否显示序号列
  showIndex?: boolean
  // 是否显示分页
  showPagination?: boolean
  // 总条数
  total?: number
  // 加载状态
  loading?: boolean
  // 分页配置
  pagination?: Pagination
}

const props = withDefaults(defineProps<Props>(), {
  border: false,
  stripe: true,
  showSelection: false,
  showIndex: true,
  showPagination: true,
  total: 0,
  loading: false,
  pagination: () => ({
    pageNo: 1,
    pageSize: 10,
    total: 0
  })
})

const emit = defineEmits(['selection-change', 'size-change', 'current-change'])

// 分页相关
const currentPage = ref(props.pagination.pageNo)
const pageSize = ref(props.pagination.pageSize)

// 选择项变化
const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}

// 每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  emit('size-change', val)
}

// 当前页变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emit('current-change', val)
}
</script>

<style scoped>
.base-table {
  width: 100%;
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
