<template>
  <div class="massScreening-detail">
    <el-card>
      <patient-info :patientInfo="detail" />
    </el-card>
    <el-card class="massScreening-detail-card">
      <div class="massScreening-detail-content-result">
        <flag-component title="结果判断" />
        <div class="result-table">
          <el-descriptions :column="3" border>
            <el-descriptions-item label-class-name="my-label">
              <template #label>糖尿病风险值</template>
              {{ detail.pValue }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template #label>糖尿病普查结果判定</template>
              {{ detail.csTnbResult }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template #label>高血压普查结果判定</template>
              {{ detail.csGxyResult }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template #label>慢阻肺普查结果判定</template>
              {{ detail.csCopdResult }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template #label>房颤普查结果判定</template>
              {{ detail.csFcResult }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template #label></template>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div class="massScreening-detail-content-questionnaire">
        <flag-component title="问卷调查" />
        <div class="questionnaire-name-list">
          <div
            v-for="(item, index) in questionnaireNames"
            :key="index"
            :class="['questionnaire-name', { active: questionnaireIndex === index }]"
            @click="handleQuestionnaireNameClick(item, index)"
          >
            {{ item }}
          </div>
        </div>
        <div class="questionnaire-content">
          <Questionnaire :questionnaireIndex="questionnaireIndex" :detail="detail" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getMassScreeningDetail } from '@/service/massScreening'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import PatientInfo from '@/components/patientInfo.vue'
import FlagComponent from '@/components/flagComponent.vue'
import Questionnaire from '@/views/massScreening/component/questionnaire.vue'
const route = useRoute()
const id = route.query.id

const detail = ref<any>({})
const questionnaireIndex = ref<number>(0)

const questionnaireNames = ['糖尿病和高血压问卷', '慢阻肺问卷', '房颤问卷']

onMounted(() => {
  getMassScreeningDetailFn()
})

const getMassScreeningDetailFn = async () => {
  const res = await getMassScreeningDetail({ id })
  if (res.code === 200) {
    detail.value = res.data
  }
}

const handleQuestionnaireNameClick = (item: string, index: number) => {
  questionnaireIndex.value = index
}
</script>

<style lang="less" scoped>
.massScreening-detail-card {
  margin-top: 16px;
  .massScreening-detail-content-result {
    .result-table {
      margin: 16px 0;
      :deep(.my-label) {
        background: #e6f9ff !important;
      }
    }
  }
  .massScreening-detail-content-questionnaire {
    .questionnaire-name-list {
      margin-top: 16px;
      display: flex;
      gap: 100px;
      .questionnaire-name {
        background: #f3f3f3;
        padding: 10px 15px;
        border-radius: 20px;
        cursor: pointer;
        color: #666;
      }
      .active {
        background: #e4f4fb;
        color: #4bc0f1;
      }
    }
    .questionnaire-content {
      margin-top: 16px;
    }
  }
}
</style>
