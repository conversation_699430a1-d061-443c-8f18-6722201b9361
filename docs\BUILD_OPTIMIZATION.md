# 构建优化说明

## 🚀 优化内容概览

本次构建优化主要包含以下几个方面：

### 1. 代码分割优化
- **手动分包**: 将 Vue 生态、UI 库、工具库等分别打包
- **动态导入**: 路由级别的懒加载已实现
- **资源分类**: 按文件类型分类存放（js/css/images/fonts/media）

### 2. 压缩优化
- **生产环境**: 使用 Terser 进行深度压缩
- **开发环境**: 使用 ESBuild 提升构建速度
- **移除调试代码**: 生产环境自动移除 console 和 debugger

### 3. 依赖优化
- **预构建**: 预构建常用依赖提升开发体验
- **外部化**: 支持 CDN 外部化大型依赖
- **Tree Shaking**: 自动移除未使用的代码

### 4. 缓存优化
- **文件指纹**: 基于内容的 hash 命名
- **长期缓存**: 分离 vendor 和业务代码
- **增量构建**: 只重新构建变更的部分

## 📊 构建分析

### 使用构建分析工具
```bash
# 构建并分析产物
npm run build:analyze

# 仅分析现有构建产物
node scripts/build-analyzer.js [dist目录路径]
```

### 分析报告内容
- 文件大小统计
- 文件类型分布
- 最大文件列表
- 压缩效果分析
- 性能建议

## 🔧 配置说明

### 环境变量配置
```bash
# .env - 通用配置
VITE_APP_TITLE=应用标题
VITE_PUBLIC_PATH=公共路径

# .env.development - 开发环境
VITE_API_BASE_URL=开发环境API地址
VITE_SHOW_DEVTOOLS=是否显示开发工具

# .env.production - 生产环境
VITE_API_BASE_URL=生产环境API地址
VITE_DROP_CONSOLE=是否移除console
```

### 构建目标
- **开发构建**: `npm run build:dev` → `dist_dev/`
- **生产构建**: `npm run build:prod` → `dist_prod/`

## 📈 性能提升

### 构建速度优化
- **并行处理**: 利用多核 CPU 并行构建
- **缓存机制**: 增量构建和依赖缓存
- **预构建**: 开发时预构建第三方依赖

### 运行时性能优化
- **代码分割**: 按需加载减少初始包大小
- **资源压缩**: Gzip/Brotli 压缩
- **缓存策略**: 合理的缓存配置

### 包大小优化
- **Tree Shaking**: 移除未使用代码
- **依赖分析**: 识别和优化大型依赖
- **按需导入**: Element Plus 等库按需导入

## 🛠️ 自定义配置

### 修改分包策略
在 `vite.config.ts` 中修改 `manualChunks` 配置：

```typescript
manualChunks: {
  // 添加新的分包
  'your-lib': ['your-library-name'],
  
  // 修改现有分包
  'vue-vendor': ['vue', 'vue-router', 'pinia'],
}
```

### 添加新的别名
```typescript
resolve: {
  alias: {
    '@': fileURLToPath(new URL('./src', import.meta.url)),
    'your-alias': fileURLToPath(new URL('./src/your-path', import.meta.url))
  }
}
```

### 自定义压缩选项
```typescript
terserOptions: {
  compress: {
    drop_console: true,
    drop_debugger: true,
    pure_funcs: ['console.log', 'console.info']
  }
}
```

## 📋 最佳实践

### 1. 依赖管理
- 定期更新依赖版本
- 移除未使用的依赖
- 使用 `npm audit` 检查安全漏洞

### 2. 代码组织
- 合理使用动态导入
- 避免循环依赖
- 保持组件粒度适中

### 3. 资源优化
- 压缩图片资源
- 使用 WebP 格式
- 合理使用字体文件

### 4. 监控分析
- 定期运行构建分析
- 监控包大小变化
- 关注构建时间

## 🔍 故障排除

### 常见问题

**Q: 构建后某些功能不工作**
A: 检查是否有动态导入的路径问题，确保生产环境路径正确

**Q: 包大小过大**
A: 运行 `npm run build:analyze` 分析大文件，考虑代码分割或外部化

**Q: 构建速度慢**
A: 检查依赖预构建配置，考虑使用更快的构建工具

**Q: 样式丢失**
A: 检查 CSS 预处理器配置和样式导入路径

### 调试技巧
1. 使用 `--debug` 参数查看详细构建信息
2. 检查 sourcemap 文件定位问题
3. 对比开发和生产环境的差异

## 📚 相关资源

- [Vite 官方文档](https://vitejs.dev/)
- [Rollup 配置指南](https://rollupjs.org/guide/en/)
- [Terser 压缩选项](https://terser.org/docs/api-reference)
- [Vue 3 性能优化](https://vuejs.org/guide/best-practices/performance.html)
