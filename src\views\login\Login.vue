<template>
  <div class="login">
    <div class="login-form">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="账号" prop="username">
          <el-input v-model="form.username" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleLogin">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { localCache } from '@/utils/cache'
import { LOGIN_Token, LOGIN_USER_INFO } from '@/utils/constants'
import { useRouter } from 'vue-router'
import cryptoJS from '@/utils/cryptoJS'
import { login } from '@/service/login'

const router = useRouter()
const rules = reactive({
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
})

const form = ref({
  username: '',
  password: ''
})

const handleLogin = () => {
  const password = cryptoJS.encrypt(form.value.password)
  login({
    phone: form.value.username,
    password,
    platform: 'pc'
  }).then((item) => {
    if (item.code === 200) {
      localCache.setCache(LOGIN_Token, item.data.access_token)
      localCache.setCache(LOGIN_USER_INFO, {
        username: item.data.username,
        userId: item.data.userid,
        departId: item.data.departId,
        departName: item.data.departName
      })
      router.push('/csp/home')
    }
  })
}
</script>

<style lang="less" scoped>
.login {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .login-form {
    width: 400px;
    height: 300px;
  }
}
</style>
