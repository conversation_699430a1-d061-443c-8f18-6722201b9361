<template>
  <el-sub-menu v-if="hasChildren(item)" :index="item.path">
    <template #title>
      <span>{{ item.title }}</span>
    </template>
    <MenuItem v-for="child in item.children" :key="child.key" :item="child" />
  </el-sub-menu>
  <el-menu-item v-else :index="item.path">
    <!-- <img :src="item.icon" alt="icon" /> -->
    <span>{{ item.title }}</span>
  </el-menu-item>
</template>

<script setup lang="ts">
import type { MenuDataItem } from '@/types/menu'

defineProps<{
  item: MenuDataItem
}>()

const hasChildren = (item: MenuDataItem) => item.children && item.children.length > 0
</script>
