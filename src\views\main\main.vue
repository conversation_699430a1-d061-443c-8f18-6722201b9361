<template>
  <div class="main">
    <el-container class="main-content">
      <el-aside width="200px">
        <MainMenu />
      </el-aside>
      <el-container>
        <el-header>
          <Breadcrumb />
        </el-header>
        <el-main>
          <el-config-provider :locale="zhCn">
            <RouterView />
          </el-config-provider>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import MainMenu from '@/components/menu/mainMenu.vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import Breadcrumb from '@/components/breadcrumb.vue'
</script>

<style lang="less" scoped>
.main {
  height: 100%;
  .main-content {
    height: 100%;
    .el-aside {
      background-color: #fff;
      height: 100%;
    }
    .el-header {
      height: 40px;
      border-bottom: 1px solid #e6e6e6;
      display: flex;
      align-items: center;
    }
    .el-main {
      padding: 20px;
    }
  }
}
</style>
