// 主题色彩变量
@primary-color: #409eff;
@success-color: #67c23a;
@warning-color: #e6a23c;
@danger-color: #f56c6c;
@info-color: #909399;

// 文本颜色
@text-color-primary: #303133;
@text-color-regular: #606266;
@text-color-secondary: #909399;
@text-color-placeholder: #c0c4cc;

// 边框颜色
@border-color-base: #dcdfe6;
@border-color-light: #e4e7ed;
@border-color-lighter: #ebeef5;
@border-color-extra-light: #f2f6fc;

// 背景色
@background-color-base: #f5f7fa;
@background-color-light: #fafafa;

// 字体大小
@font-size-extra-large: 20px;
@font-size-large: 18px;
@font-size-medium: 16px;
@font-size-base: 14px;
@font-size-small: 13px;
@font-size-extra-small: 12px;

// 间距
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;

// 圆角
@border-radius-base: 4px;
@border-radius-small: 2px;
@border-radius-large: 6px;

// 阴影
@box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
@box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 动画时间
@transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
@transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);

// Z-index 层级
@z-index-normal: 1;
@z-index-top: 1000;
@z-index-popper: 2000;

// 布局相关
@header-height: 60px;
@sidebar-width: 200px;
@sidebar-collapsed-width: 64px;

// 响应式断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;
