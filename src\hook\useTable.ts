import { ref, reactive } from 'vue'

interface Pagination {
  pageNo: number
  pageSize: number
  total: number
}

interface UseTableOptions<T> {
  fetchData: (params: any) => Promise<{
    list: T[]
    total: number
  }>
  defaultPageSize?: number
  immediate?: boolean
  extraParams?: Record<string, any>
}

export function useTable<T = any>(options: UseTableOptions<T>) {
  const tableData = ref<T[]>([])
  const loading = ref(false)

  const pagination = reactive<Pagination>({
    pageNo: 1,
    pageSize: options.defaultPageSize || 10,
    total: 0
  })

  const extraParams = ref<Record<string, any>>(options.extraParams || {})

  const getList = async () => {
    loading.value = true
    try {
      const res = await options.fetchData({
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        ...extraParams.value
      })

      tableData.value = res.list
      pagination.total = res.total
    } finally {
      loading.value = false
    }
  }

  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNo = 1
    getList()
  }

  const handleCurrentChange = (page: number) => {
    pagination.pageNo = page
    getList()
  }

  const reload = () => {
    pagination.pageNo = 1
    getList()
  }

  if (options.immediate !== false) {
    getList()
  }

  return {
    tableData,
    loading,
    pagination,
    getList,
    reload,
    handleSizeChange,
    handleCurrentChange,
    setExtraParams: (params: Record<string, any>) => {
      extraParams.value = params
    }
  }
}
