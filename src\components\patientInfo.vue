<template>
  <div class="patient-info">
    <div class="patient-info-item">
      <div class="patient-info-item-name">{{ patientInfo.name }}</div>
    </div>
    <div class="patient-info-item">
      <div class="patient-info-item-label">年龄：</div>
      <div class="patient-info-item-value">{{ patientInfo.age }}</div>
    </div>
    <div class="patient-info-item">
      <div class="patient-info-item-label">性别：</div>
      <div class="patient-info-item-value">{{ patientInfo.sex === 1 ? '男' : '女' }}</div>
    </div>
    <div class="patient-info-item">
      <div class="patient-info-item-label">手机号码：</div>
      <div class="patient-info-item-value">{{ patientInfo.phone }}</div>
    </div>
    <div class="patient-info-item">
      <el-tag
        v-for="tag in diseaseTags"
        :key="tag"
        :type="
          tag === '高血压'
            ? 'danger'
            : tag === '糖尿病'
              ? 'success'
              : tag === '慢阻肺'
                ? 'warning'
                : 'info'
        "
        >{{ tag }}</el-tag
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps<{ patientInfo: any }>()

const diseaseTags = computed(() => {
  return props.patientInfo?.disease?.split(',')
})
</script>

<style lang="less" scoped>
.patient-info {
  display: flex;
  gap: 80px;
  .patient-info-item {
    display: flex;
    .patient-info-item-name {
      font-size: 20px;
    }
    .patient-info-item-label,
    .patient-info-item-value {
      font-size: 16px;
      font-weight: 400;
    }
    .patient-info-item-label {
      width: 80px;
    }
  }
}
</style>
