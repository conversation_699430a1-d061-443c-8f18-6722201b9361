<template>
  <div class="disease-category">
    <div
      v-for="item in diseaseList"
      :key="item.diseaseCode"
      :class="['stat-card', { active: item.diseaseCode === activeCode }]"
      @click="handleClick(item)"
    >
      <img v-if="item.icon" :src="item.icon" class="stat-card-icon" />
      <div class="stat-card-title">{{ item.diseaseName }}</div>
      <div class="stat-card-value">{{ item.count }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getMassScreeningDisease } from '@/service/massScreening'
import { onMounted, ref } from 'vue'
import alls from '@/assets/img/alls.png'
import copd from '@/assets/img/copd.png'
import gxy from '@/assets/img/gxy.png'
import tnb from '@/assets/img/tnb.png'
import fcy from '@/assets/img/fc.png'

const diseaseList = ref<any[]>([])
const activeCode = ref('all')
const emit = defineEmits(['change'])

const getDiseaseList = async () => {
  const res = await getMassScreeningDisease({})
  if (res.code === 200) {
    const list = res.data || []
    const all = list.reduce(
      (acc: any, curr: any) => {
        return {
          diseaseName: '全部',
          diseaseCode: 'all',
          count: acc.count + curr.count
        }
      },
      {
        diseaseName: '全部',
        diseaseCode: 'all',
        count: 0
      }
    )
    const diseaseListTemp = [all, ...list]
    diseaseList.value = diseaseListTemp.map((item: any) => {
      if (item.diseaseCode === 'all') {
        item.icon = alls
      } else if (item.diseaseCode === 'copd') {
        item.icon = copd
      } else if (item.diseaseCode === 'gxy') {
        item.icon = gxy
      } else if (item.diseaseCode === 'tnb') {
        item.icon = tnb
      } else {
        item.icon = fcy
      }
      return item
    })
  }
}

const handleClick = (item: any) => {
  activeCode.value = item.diseaseCode
  emit('change', item)
}

onMounted(() => {
  getDiseaseList()
})

defineExpose({
  activeCode
})
</script>

<style lang="less" scoped>
.disease-category {
  display: flex;
  align-items: center;
  gap: 10px;
  .stat-card {
    display: flex;
    align-items: center;
    background: #f4f4f4;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    min-width: 130px;
    gap: 8px;
    transition:
      border 0.2s,
      background 0.2s;
  }
  .stat-card.active {
    background: #2db7f5;
    color: #fff;
    border-color: #2db7f5;
  }
  .stat-card-icon {
    width: 24px;
    height: 24px;
  }
  .stat-card-title {
    font-size: 14px;
  }
  .stat-card-value {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>
