<template>
  <div class="search">
    <el-card>
      <disease-category @change="handleDiseaseChange" ref="diseaseCategoryRef" />
    </el-card>
    <el-card class="massScreening-search">
      <el-form :model="searchForm" label-width="100px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="普查时间">
              <el-date-picker
                v-model="searchForm.timeRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="P值">
              <el-row>
                <el-col :span="11">
                  <el-input v-model="searchForm.pValueMin" placeholder="最小值" />
                </el-col>
                <el-col :span="2" style="text-align: center">
                  <span>~</span>
                </el-col>
                <el-col :span="11">
                  <el-input v-model="searchForm.pValueMax" placeholder="最大值" />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="普查状态">
              <el-select v-model="searchForm.csStatus" placeholder="请选择">
                <el-option label="已筛查" value="1" />
                <el-option label="已转居民档案" value="5" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
  <div class="massScreening-table">
    <el-card>
      <base-table
        :table-data="tableData"
        :columns="columns"
        :pagination="pagination"
        :loading="loading"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <!-- 普查状态 -->
        <template #csStatus="{ row }">
          <span v-if="row.csGxyStatus === 1">已筛查</span>
          <span v-else>已转居民档案</span>
        </template>
        <!-- 普查结果 -->
        <template #csResult="{ row }">
          {{ `${row.csGxyResult} ${row.csTnbResult} ${row.csCopdResult}` }}
        </template>
        <!-- 操作 -->
        <template #operation="{ row }">
          <el-button type="primary" link @click="handleDetail(row)">详情</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useTable } from '@/hook/useTable'
import { reactive, ref } from 'vue'
import BaseTable from '@/components/common/BaseTable.vue'
import { useRouter } from 'vue-router'
import { getMassScreeningList, deleteMassScreening } from '@/service/massScreening'
import DiseaseCategory from '@/components/diseaseCategory.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const diseaseCategoryRef = ref<InstanceType<typeof DiseaseCategory>>()
const router = useRouter()
const getTableData = async (params: any) => {
  const res = await getMassScreeningList(params)
  if (res.code === 200) {
    return {
      list: res.data.list,
      total: res.data.total
    }
  } else {
    return {
      list: [],
      total: 0
    }
  }
}

const {
  tableData,
  loading,
  pagination,
  reload,
  handleSizeChange,
  handleCurrentChange,
  setExtraParams
} = useTable({
  fetchData: (params: any) => {
    return getTableData(params)
  }
})

const searchForm = reactive({
  timeRange: [],
  pValueMin: '',
  pValueMax: '',
  csStatus: '',
  disease: ''
})

// 表格列配置
const columns = [
  { prop: 'name', label: '姓名' },
  { prop: 'age', label: '年龄' },
  { prop: 'idCard', label: '身份证号', width: 180 },
  { prop: 'phone', label: '手机号码' },
  { prop: 'address', label: '地址' },
  { prop: 'disease', label: '慢病种类' },
  { prop: 'csDate', label: '普筛日期' },
  { prop: 'pValue', label: 'P值' },
  { prop: 'csResult', label: '普查结果', showOverflowTooltip: true, width: 200, slot: 'csResult' },
  { prop: 'csStatus', label: '普查状态', slot: 'csStatus' },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'operation', label: '操作', slot: 'operation' }
]

const handleDetail = (row: any) => {
  console.log(row)
  console.log(router)
  router.push({
    name: 'massScreeningDetail',
    query: {
      id: row.id
    }
  })
}

const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定删除该人群筛查吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const res = await deleteMassScreening({ id: row.id })
      if (res.code === 200) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        reload()
      } else {
        ElMessage({
          type: 'error',
          message: res.message
        })
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      })
    })
}

const handleDiseaseChange = (disease: any) => {
  searchForm.disease = disease.diseaseCode
}

const handleSearch = () => {
  setExtraParams({
    pValueMin: searchForm.pValueMin,
    pValueMax: searchForm.pValueMax,
    csStatus: searchForm.csStatus,
    startDate: searchForm.timeRange[0],
    endDate: searchForm.timeRange[1],
    disease: searchForm.disease === 'all' ? '' : searchForm.disease
  })
  reload()
}

const handleReset = () => {
  searchForm.pValueMin = ''
  searchForm.pValueMax = ''
  searchForm.csStatus = ''
  searchForm.timeRange = []
  searchForm.disease = ''
  setExtraParams({
    pValueMin: '',
    pValueMax: '',
    csStatus: '',
    disease: ''
  })
  diseaseCategoryRef.value!.activeCode = 'all'
  reload()
}
</script>

<style lang="less" scoped>
.search :deep(.el-form-item) {
  margin-bottom: 0;
}
.massScreening-search {
  margin-top: 16px;
}
.massScreening-table {
  padding-top: 16px;
}
</style>
