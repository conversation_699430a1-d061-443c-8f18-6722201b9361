import { createRouter, createWebHashHistory } from 'vue-router'
import { localCache } from '@/utils/cache'
import { LOGIN_Token } from '@/utils/constants'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      redirect: '/csp'
    },
    {
      path: '/csp/login',
      component: () => import('../views/login/Login.vue')
    },
    {
      path: '/csp',
      component: () => import('../views/main/main.vue'),
      redirect: '/csp/home',
      children: [
        {
          name: 'home',
          path: 'home',
          component: () => import('../views/home/<USER>'),
          meta: {
            title: '首页'
          }
        },
        {
          name: 'massScreening',
          path: 'massScreening',
          component: () => import('@/views/massScreening/index.vue'),
          meta: {
            title: '人群筛查'
          }
        },
        {
          name: 'massScreeningDetail',
          path: 'massScreeningDetail',
          component: () => import('@/views/massScreening/detail.vue'),
          meta: {
            title: '人群筛查详情'
          }
        }
      ]
    },

    {
      path: '/:pathMatch(.*)',
      component: () => import('../views/not-found/NotFound.vue')
    }
  ]
})

//导航守卫
router.beforeEach((to, from, next) => {
  const token = localCache.getCache(LOGIN_Token)

  const isLoginPage = to.path === '/csp/login'

  if (!token && !isLoginPage) {
    next('/csp/login')
  } else if (token && isLoginPage) {
    next('/')
  } else {
    next()
  }
})

export default router
