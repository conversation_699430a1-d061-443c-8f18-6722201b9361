#!/usr/bin/env node

/**
 * 构建分析脚本
 * 用于分析构建产物的大小和依赖关系
 */

import { readFileSync, readdirSync, statSync } from 'fs'
import { join, extname } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 获取文件大小的可读格式
function getReadableSize(bytes) {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 递归获取目录下所有文件
function getAllFiles(dir, fileList = []) {
  const files = readdirSync(dir)
  
  files.forEach(file => {
    const filePath = join(dir, file)
    const stat = statSync(filePath)
    
    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList)
    } else {
      fileList.push({
        path: filePath,
        size: stat.size,
        ext: extname(file)
      })
    }
  })
  
  return fileList
}

// 分析构建产物
function analyzeBuild(distPath) {
  console.log('🔍 开始分析构建产物...\n')
  
  try {
    const files = getAllFiles(distPath)
    
    // 按文件类型分组
    const filesByType = {}
    let totalSize = 0
    
    files.forEach(file => {
      const type = file.ext || 'other'
      if (!filesByType[type]) {
        filesByType[type] = []
      }
      filesByType[type].push(file)
      totalSize += file.size
    })
    
    console.log('📊 构建产物分析结果:')
    console.log('=' * 50)
    console.log(`总文件数: ${files.length}`)
    console.log(`总大小: ${getReadableSize(totalSize)}\n`)
    
    // 按类型显示统计
    Object.keys(filesByType).sort().forEach(type => {
      const typeFiles = filesByType[type]
      const typeSize = typeFiles.reduce((sum, file) => sum + file.size, 0)
      const percentage = ((typeSize / totalSize) * 100).toFixed(2)
      
      console.log(`${type.padEnd(10)} | ${typeFiles.length.toString().padStart(3)} 个文件 | ${getReadableSize(typeSize).padStart(10)} | ${percentage}%`)
    })
    
    console.log('\n📈 最大的文件:')
    console.log('-' * 50)
    
    // 显示最大的10个文件
    files
      .sort((a, b) => b.size - a.size)
      .slice(0, 10)
      .forEach((file, index) => {
        const relativePath = file.path.replace(distPath, '').replace(/\\/g, '/')
        console.log(`${(index + 1).toString().padStart(2)}. ${relativePath.padEnd(40)} ${getReadableSize(file.size).padStart(10)}`)
      })
    
    // 检查是否有过大的文件
    const largeFiles = files.filter(file => file.size > 1024 * 1024) // 大于1MB
    if (largeFiles.length > 0) {
      console.log('\n⚠️  发现大文件 (>1MB):')
      console.log('-' * 50)
      largeFiles.forEach(file => {
        const relativePath = file.path.replace(distPath, '').replace(/\\/g, '/')
        console.log(`   ${relativePath} - ${getReadableSize(file.size)}`)
      })
      console.log('\n💡 建议: 考虑对大文件进行代码分割或压缩优化')
    }
    
    // 检查 gzip 压缩效果（如果有 .gz 文件）
    const gzFiles = files.filter(file => file.ext === '.gz')
    if (gzFiles.length > 0) {
      console.log('\n📦 Gzip 压缩效果:')
      console.log('-' * 50)
      gzFiles.forEach(gzFile => {
        const originalFile = files.find(f => f.path === gzFile.path.replace('.gz', ''))
        if (originalFile) {
          const compressionRatio = ((1 - gzFile.size / originalFile.size) * 100).toFixed(2)
          const relativePath = gzFile.path.replace(distPath, '').replace(/\\/g, '/').replace('.gz', '')
          console.log(`   ${relativePath.padEnd(40)} ${compressionRatio}% 压缩`)
        }
      })
    }
    
  } catch (error) {
    console.error('❌ 分析失败:', error.message)
    process.exit(1)
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2)
  const distPath = args[0] || join(__dirname, '../dist_prod')
  
  console.log(`📁 分析目录: ${distPath}\n`)
  
  try {
    statSync(distPath)
  } catch (error) {
    console.error(`❌ 目录不存在: ${distPath}`)
    console.log('💡 请先运行构建命令: npm run build:prod')
    process.exit(1)
  }
  
  analyzeBuild(distPath)
}

// 运行脚本
main()
