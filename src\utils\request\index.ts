// request/index.ts
import Request from './request'
import type { CreateRequestConfig } from './type'
import { getBaseUrl } from '@/utils/env'

const config: CreateRequestConfig = {
  baseURL: getBaseUrl(import.meta.env.MODE), // 你的接口前缀，可按需替换
  timeout: 10000 // 请求超时时间
  // interceptors: {
  //   // 可选：你可以传入自己的拦截器
  //   requestSuccessFn: (config) => {
  //     console.log('自定义请求成功拦截器')

  //     return config
  //   },
  //   responseSuccessFn: (res) => {
  //     console.log('自定义响应成功拦截器')
  //     return res
  //   }
  // }
}

const request = new Request(config)

export default request
